//
//  MyLabel.m
//  JDTMyButtonModule
//
//  Created by lvchenzhu.1 on 2025/7/22.
//

#import "MyLabel.h"

@implementation MyLabel

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        // Debug bundle path
        NSBundle *bundle = [NSBundle bundleForClass:[self class]];
        NSLog(@"[JDTMyButtonModule] Main bundle path: %@", bundle.bundlePath);
        
        // Get resource bundle path with fallback logic
        NSString *resourceBundlePath = [bundle pathForResource:@"JDTMyButtonModule" ofType:@"bundle"];
        NSLog(@"[JDTMyButtonModule] Resource bundle path: %@", resourceBundlePath);
        
        // Fallback to main bundle if resource bundle not found
        NSBundle *resourceBundle = resourceBundlePath ? [NSBundle bundleWithPath:resourceBundlePath] : bundle;
        
        self.text = NSLocalizedStringFromTableInBundle(@"button2", @"Localizable", resourceBundle, nil);
    }
    return self;
}

@end
