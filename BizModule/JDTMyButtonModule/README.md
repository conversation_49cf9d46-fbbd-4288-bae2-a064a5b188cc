# JDTMyButtonModule

[![CI Status](https://img.shields.io/travis/lvchenzhu.1/JDTMyButtonModule.svg?style=flat)](https://travis-ci.org/lvchenzhu.1/JDTMyButtonModule)
[![Version](https://img.shields.io/cocoapods/v/JDTMyButtonModule.svg?style=flat)](https://cocoapods.org/pods/JDTMyButtonModule)
[![License](https://img.shields.io/cocoapods/l/JDTMyButtonModule.svg?style=flat)](https://cocoapods.org/pods/JDTMyButtonModule)
[![Platform](https://img.shields.io/cocoapods/p/JDTMyButtonModule.svg?style=flat)](https://cocoapods.org/pods/JDTMyButtonModule)

## Example

To run the example project, clone the repo, and run `pod install` from the Example directory first.

## Requirements

## Installation

JDTMyButtonModule is available through [CocoaPods](https://cocoapods.org). To install
it, simply add the following line to your Podfile:

```ruby
pod 'JDTMyButtonModule'
```

## Author

lvchenzhu.1, <EMAIL>

## License

JDTMyButtonModule is available under the MIT license. See the LICENSE file for more info.
