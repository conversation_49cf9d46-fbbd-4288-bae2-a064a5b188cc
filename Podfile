# Uncomment the next line to define a global platform for your project
platform :ios, '10.0'

install! 'cocoapods',
  preserve_pod_file_structure: true,     # 保持库的原有目录结构
  generate_multiple_pod_projects: true,  # 每个 Pod 独立项目
  deterministic_uuids: false            # 消除执行pod install警告：[Xcodeproj] Generated duplicate UUIDs

target 'CMSDemo' do
  # Comment the next line if you don't want to use dynamic frameworks
  use_frameworks!

  # Pods for CMSDemo
  pod 'JDTMyButtonModule', :path => './BizModule/JDTMyButtonModule'
  
end
