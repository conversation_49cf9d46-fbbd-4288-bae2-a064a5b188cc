// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		46EB2E00000180 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 46EB2E00000170 /* Foundation.framework */; };
		46EB2E000001F0 /* Pods-CMSDemo-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 46EB2E000001E0 /* Pods-CMSDemo-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		46EB2E00000240 /* Pods-CMSDemo-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 46EB2E00000230 /* Pods-CMSDemo-dummy.m */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		46EB2E00000250 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 46EB2E000000C0 /* JDTMyButtonModule.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = 2A1E01B42BF8B6D39AC92D85A26F40D4;
			remoteInfo = JDTMyButtonModule;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		0B75A43121104A520FBAD294322B47BB /* Pods-CMSDemo */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = "Pods-CMSDemo"; path = Pods_CMSDemo.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		46EB2E000000B0 /* Podfile */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = Podfile; path = ../Podfile; sourceTree = SOURCE_ROOT; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		46EB2E000000C0 /* JDTMyButtonModule */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = JDTMyButtonModule; path = JDTMyButtonModule.xcodeproj; sourceTree = "<group>"; };
		46EB2E00000170 /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Foundation.framework; sourceTree = DEVELOPER_DIR; };
		46EB2E000001A0 /* Pods-CMSDemo.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-CMSDemo.release.xcconfig"; sourceTree = "<group>"; };
		46EB2E000001B0 /* Pods-CMSDemo.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-CMSDemo.debug.xcconfig"; sourceTree = "<group>"; };
		46EB2E000001C0 /* Pods-CMSDemo-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-CMSDemo-Info.plist"; sourceTree = "<group>"; };
		46EB2E000001D0 /* Pods-CMSDemo.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = "Pods-CMSDemo.modulemap"; sourceTree = "<group>"; };
		46EB2E000001E0 /* Pods-CMSDemo-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Pods-CMSDemo-umbrella.h"; sourceTree = "<group>"; };
		46EB2E00000200 /* Pods-CMSDemo-frameworks.sh */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.script.sh; path = "Pods-CMSDemo-frameworks.sh"; sourceTree = "<group>"; };
		46EB2E00000210 /* Pods-CMSDemo-acknowledgements.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-CMSDemo-acknowledgements.plist"; sourceTree = "<group>"; };
		46EB2E00000220 /* Pods-CMSDemo-acknowledgements.markdown */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; path = "Pods-CMSDemo-acknowledgements.markdown"; sourceTree = "<group>"; };
		46EB2E00000230 /* Pods-CMSDemo-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Pods-CMSDemo-dummy.m"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		46EB2E00000140 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				46EB2E00000180 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		46EB2E00000010 = {
			isa = PBXGroup;
			children = (
				46EB2E000000B0 /* Podfile */,
				46EB2E00000090 /* Development Pods */,
				46EB2E00000060 /* Frameworks */,
				46EB2E00000020 /* Products */,
				46EB2E00000070 /* Targets Support Files */,
			);
			sourceTree = "<group>";
		};
		46EB2E00000020 /* Products */ = {
			isa = PBXGroup;
			children = (
				0B75A43121104A520FBAD294322B47BB /* Pods-CMSDemo */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		46EB2E00000060 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				46EB2E00000160 /* iOS */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		46EB2E00000070 /* Targets Support Files */ = {
			isa = PBXGroup;
			children = (
				46EB2E00000190 /* Pods-CMSDemo */,
			);
			name = "Targets Support Files";
			sourceTree = "<group>";
		};
		46EB2E00000090 /* Development Pods */ = {
			isa = PBXGroup;
			children = (
				46EB2E000000C0 /* JDTMyButtonModule */,
			);
			name = "Development Pods";
			sourceTree = "<group>";
		};
		46EB2E00000160 /* iOS */ = {
			isa = PBXGroup;
			children = (
				46EB2E00000170 /* Foundation.framework */,
			);
			name = iOS;
			sourceTree = "<group>";
		};
		46EB2E00000190 /* Pods-CMSDemo */ = {
			isa = PBXGroup;
			children = (
				46EB2E000001D0 /* Pods-CMSDemo.modulemap */,
				46EB2E00000220 /* Pods-CMSDemo-acknowledgements.markdown */,
				46EB2E00000210 /* Pods-CMSDemo-acknowledgements.plist */,
				46EB2E00000230 /* Pods-CMSDemo-dummy.m */,
				46EB2E00000200 /* Pods-CMSDemo-frameworks.sh */,
				46EB2E000001C0 /* Pods-CMSDemo-Info.plist */,
				46EB2E000001E0 /* Pods-CMSDemo-umbrella.h */,
				46EB2E000001B0 /* Pods-CMSDemo.debug.xcconfig */,
				46EB2E000001A0 /* Pods-CMSDemo.release.xcconfig */,
			);
			name = "Pods-CMSDemo";
			path = "Target Support Files/Pods-CMSDemo";
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		46EB2E00000120 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				46EB2E000001F0 /* Pods-CMSDemo-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		9AC691CB8F1274BF8B600D69B0C6518D /* Pods-CMSDemo */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 46EB2E000000E0 /* Build configuration list for PBXNativeTarget "Pods-CMSDemo" */;
			buildPhases = (
				46EB2E00000120 /* Headers */,
				46EB2E00000130 /* Sources */,
				46EB2E00000140 /* Frameworks */,
				46EB2E00000150 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				46EB2E00000260 /* PBXTargetDependency */,
			);
			name = "Pods-CMSDemo";
			productName = Pods_CMSDemo;
			productReference = 0B75A43121104A520FBAD294322B47BB /* Pods-CMSDemo */;
			productType = "com.apple.product-type.framework";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		46EB2E00000000 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1600;
				LastUpgradeCheck = 1600;
			};
			buildConfigurationList = 46EB2E00000030 /* Build configuration list for PBXProject "Pods" */;
			compatibilityVersion = "Xcode 16.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 46EB2E00000010;
			minimizedProjectReferenceProxies = 0;
			preferredProjectObjectVersion = 77;
			productRefGroup = 46EB2E00000020 /* Products */;
			projectDirPath = "";
			projectReferences = (
				{
					ProjectRef = 46EB2E000000C0 /* JDTMyButtonModule */;
				},
			);
			projectRoot = "";
			targets = (
				9AC691CB8F1274BF8B600D69B0C6518D /* Pods-CMSDemo */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		46EB2E00000150 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		46EB2E00000130 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				46EB2E00000240 /* Pods-CMSDemo-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		46EB2E00000260 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = JDTMyButtonModule;
			targetProxy = 46EB2E00000250 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		46EB2E00000040 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_DEBUG=1",
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Debug;
		};
		46EB2E00000050 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_RELEASE=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Release;
		};
		46EB2E000000F0 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 46EB2E000001A0 /* Pods-CMSDemo.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-CMSDemo/Pods-CMSDemo-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-CMSDemo/Pods-CMSDemo.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		46EB2E00000100 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 46EB2E000001B0 /* Pods-CMSDemo.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-CMSDemo/Pods-CMSDemo-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-CMSDemo/Pods-CMSDemo.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		46EB2E00000030 /* Build configuration list for PBXProject "Pods" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				46EB2E00000040 /* Debug */,
				46EB2E00000050 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		46EB2E000000E0 /* Build configuration list for PBXNativeTarget "Pods-CMSDemo" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				46EB2E00000100 /* Debug */,
				46EB2E000000F0 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 46EB2E00000000 /* Project object */;
}
