// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 71;
	objects = {

/* Begin PBXBuildFile section */
		F422DD000001E0 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = F422DD000001D0 /* Foundation.framework */; };
		F422DD00000270 /* Localizable.xcstrings in Resources */ = {isa = PBXBuildFile; fileRef = F422DD000000E0 /* Localizable.xcstrings */; };
		F422DD000002A0 /* MyLabel.m in Sources */ = {isa = PBXBuildFile; fileRef = F422DD000000D0 /* MyLabel.m */; };
		F422DD000002B0 /* MyLabel.h in Headers */ = {isa = PBXBuildFile; fileRef = F422DD000000C0 /* MyLabel.h */; settings = {ATTRIBUTES = (Public, ); }; };
		F422DD00000300 /* JDTMyButtonModule-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = F422DD000002F0 /* JDTMyButtonModule-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		F422DD00000340 /* JDTMyButtonModule-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = F422DD00000330 /* JDTMyButtonModule-dummy.m */; };
		F422DD00000370 /* JDTMyButtonModule.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 7577E9B55C52C71EADBC0FBE80753A32 /* JDTMyButtonModule.bundle */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		F422DD00000350 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = F422DD00000000 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = E4F5763B8400E84A300703D906939BE3;
			remoteInfo = "JDTMyButtonModule-JDTMyButtonModule";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		7577E9B55C52C71EADBC0FBE80753A32 /* JDTMyButtonModule.bundle */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = JDTMyButtonModule.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		C9F903850D4368B24B98D37BB8183321 /* JDTMyButtonModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = JDTMyButtonModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		F422DD000000C0 /* MyLabel.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = MyLabel.h; path = JDTMyButtonModule/Classes/MyLabel.h; sourceTree = "<group>"; };
		F422DD000000D0 /* MyLabel.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = MyLabel.m; path = JDTMyButtonModule/Classes/MyLabel.m; sourceTree = "<group>"; };
		F422DD000000E0 /* Localizable.xcstrings */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.json.xcstrings; name = Localizable.xcstrings; path = JDTMyButtonModule/Assets/Localizable.xcstrings; sourceTree = "<group>"; };
		F422DD00000100 /* JDTMyButtonModule.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; path = JDTMyButtonModule.podspec; sourceTree = "<group>"; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		F422DD00000110 /* LICENSE */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; path = LICENSE; sourceTree = "<group>"; };
		F422DD00000120 /* README.md */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = net.daringfireball.markdown; path = README.md; sourceTree = "<group>"; };
		F422DD000001D0 /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Foundation.framework; sourceTree = DEVELOPER_DIR; };
		F422DD00000290 /* ResourceBundle-JDTMyButtonModule-JDTMyButtonModule-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ResourceBundle-JDTMyButtonModule-JDTMyButtonModule-Info.plist"; sourceTree = "<group>"; };
		F422DD000002C0 /* JDTMyButtonModule.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = JDTMyButtonModule.debug.xcconfig; sourceTree = "<group>"; };
		F422DD000002D0 /* JDTMyButtonModule.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = JDTMyButtonModule.release.xcconfig; sourceTree = "<group>"; };
		F422DD000002E0 /* JDTMyButtonModule.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = JDTMyButtonModule.modulemap; sourceTree = "<group>"; };
		F422DD000002F0 /* JDTMyButtonModule-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "JDTMyButtonModule-umbrella.h"; sourceTree = "<group>"; };
		F422DD00000310 /* JDTMyButtonModule-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "JDTMyButtonModule-Info.plist"; sourceTree = "<group>"; };
		F422DD00000320 /* JDTMyButtonModule-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "JDTMyButtonModule-prefix.pch"; sourceTree = "<group>"; };
		F422DD00000330 /* JDTMyButtonModule-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "JDTMyButtonModule-dummy.m"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		F422DD000001A0 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F422DD000001E0 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F422DD00000250 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		F422DD00000010 = {
			isa = PBXGroup;
			children = (
				F422DD00000060 /* Frameworks */,
				F422DD000000B0 /* JDTMyButtonModule */,
				F422DD00000020 /* Products */,
			);
			sourceTree = "<group>";
		};
		F422DD00000020 /* Products */ = {
			isa = PBXGroup;
			children = (
				C9F903850D4368B24B98D37BB8183321 /* JDTMyButtonModule.framework */,
				7577E9B55C52C71EADBC0FBE80753A32 /* JDTMyButtonModule.bundle */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		F422DD00000060 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				F422DD000001C0 /* iOS */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		F422DD000000B0 /* JDTMyButtonModule */ = {
			isa = PBXGroup;
			children = (
				F422DD000000E0 /* Localizable.xcstrings */,
				F422DD000000C0 /* MyLabel.h */,
				F422DD000000D0 /* MyLabel.m */,
				F422DD000000F0 /* Pod */,
				F422DD00000280 /* Support Files */,
			);
			name = JDTMyButtonModule;
			path = ../BizModule/JDTMyButtonModule;
			sourceTree = "<group>";
		};
		F422DD000000F0 /* Pod */ = {
			isa = PBXGroup;
			children = (
				F422DD00000100 /* JDTMyButtonModule.podspec */,
				F422DD00000110 /* LICENSE */,
				F422DD00000120 /* README.md */,
			);
			name = Pod;
			sourceTree = "<group>";
		};
		F422DD000001C0 /* iOS */ = {
			isa = PBXGroup;
			children = (
				F422DD000001D0 /* Foundation.framework */,
			);
			name = iOS;
			sourceTree = "<group>";
		};
		F422DD00000280 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				F422DD000002E0 /* JDTMyButtonModule.modulemap */,
				F422DD00000330 /* JDTMyButtonModule-dummy.m */,
				F422DD00000310 /* JDTMyButtonModule-Info.plist */,
				F422DD00000320 /* JDTMyButtonModule-prefix.pch */,
				F422DD000002F0 /* JDTMyButtonModule-umbrella.h */,
				F422DD000002C0 /* JDTMyButtonModule.debug.xcconfig */,
				F422DD000002D0 /* JDTMyButtonModule.release.xcconfig */,
				F422DD00000290 /* ResourceBundle-JDTMyButtonModule-JDTMyButtonModule-Info.plist */,
			);
			name = "Support Files";
			path = "../../Pods/Target Support Files/JDTMyButtonModule";
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		F422DD00000180 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F422DD00000300 /* JDTMyButtonModule-umbrella.h in Headers */,
				F422DD000002B0 /* MyLabel.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		2A1E01B42BF8B6D39AC92D85A26F40D4 /* JDTMyButtonModule */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F422DD00000140 /* Build configuration list for PBXNativeTarget "JDTMyButtonModule" */;
			buildPhases = (
				F422DD00000180 /* Headers */,
				F422DD00000190 /* Sources */,
				F422DD000001A0 /* Frameworks */,
				F422DD000001B0 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				F422DD00000360 /* PBXTargetDependency */,
			);
			name = JDTMyButtonModule;
			productName = JDTMyButtonModule;
			productReference = C9F903850D4368B24B98D37BB8183321 /* JDTMyButtonModule.framework */;
			productType = "com.apple.product-type.framework";
		};
		E4F5763B8400E84A300703D906939BE3 /* JDTMyButtonModule-JDTMyButtonModule */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F422DD00000200 /* Build configuration list for PBXNativeTarget "JDTMyButtonModule-JDTMyButtonModule" */;
			buildPhases = (
				F422DD00000240 /* Sources */,
				F422DD00000250 /* Frameworks */,
				F422DD00000260 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "JDTMyButtonModule-JDTMyButtonModule";
			productName = JDTMyButtonModule;
			productReference = 7577E9B55C52C71EADBC0FBE80753A32 /* JDTMyButtonModule.bundle */;
			productType = "com.apple.product-type.bundle";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		F422DD00000000 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1600;
				LastUpgradeCheck = 1600;
			};
			buildConfigurationList = F422DD00000030 /* Build configuration list for PBXProject "JDTMyButtonModule" */;
			compatibilityVersion = "Xcode 16.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				Base,
				en,
			);
			mainGroup = F422DD00000010;
			productRefGroup = F422DD00000020 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				2A1E01B42BF8B6D39AC92D85A26F40D4 /* JDTMyButtonModule */,
				E4F5763B8400E84A300703D906939BE3 /* JDTMyButtonModule-JDTMyButtonModule */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		F422DD000001B0 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F422DD00000370 /* JDTMyButtonModule.bundle in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F422DD00000260 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F422DD00000270 /* Localizable.xcstrings in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		F422DD00000190 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F422DD00000340 /* JDTMyButtonModule-dummy.m in Sources */,
				F422DD000002A0 /* MyLabel.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F422DD00000240 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		F422DD00000360 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "JDTMyButtonModule-JDTMyButtonModule";
			target = E4F5763B8400E84A300703D906939BE3 /* JDTMyButtonModule-JDTMyButtonModule */;
			targetProxy = F422DD00000350 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		F422DD00000040 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_DEBUG=1",
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Debug;
		};
		F422DD00000050 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_RELEASE=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Release;
		};
		F422DD00000150 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F422DD000002D0 /* JDTMyButtonModule.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/JDTMyButtonModule/JDTMyButtonModule-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/JDTMyButtonModule/JDTMyButtonModule-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				LOCALIZED_STRING_MACRO_NAMES = (
					NSLocalizedString,
					CFCopyLocalizedString,
					NSLocalizedStringFromTableInBundle,
				);
				MODULEMAP_FILE = "Target Support Files/JDTMyButtonModule/JDTMyButtonModule.modulemap";
				PRODUCT_MODULE_NAME = JDTMyButtonModule;
				PRODUCT_NAME = JDTMyButtonModule;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		F422DD00000160 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F422DD000002C0 /* JDTMyButtonModule.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/JDTMyButtonModule/JDTMyButtonModule-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/JDTMyButtonModule/JDTMyButtonModule-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				LOCALIZED_STRING_MACRO_NAMES = (
					NSLocalizedString,
					CFCopyLocalizedString,
					NSLocalizedStringFromTableInBundle,
				);
				MODULEMAP_FILE = "Target Support Files/JDTMyButtonModule/JDTMyButtonModule.modulemap";
				PRODUCT_MODULE_NAME = JDTMyButtonModule;
				PRODUCT_NAME = JDTMyButtonModule;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		F422DD00000210 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F422DD000002D0 /* JDTMyButtonModule.release.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/JDTMyButtonModule";
				IBSC_MODULE = JDTMyButtonModule;
				INFOPLIST_FILE = "Target Support Files/JDTMyButtonModule/ResourceBundle-JDTMyButtonModule-JDTMyButtonModule-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				PRODUCT_NAME = JDTMyButtonModule;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		F422DD00000220 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F422DD000002C0 /* JDTMyButtonModule.debug.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/JDTMyButtonModule";
				IBSC_MODULE = JDTMyButtonModule;
				INFOPLIST_FILE = "Target Support Files/JDTMyButtonModule/ResourceBundle-JDTMyButtonModule-JDTMyButtonModule-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				PRODUCT_NAME = JDTMyButtonModule;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		F422DD00000030 /* Build configuration list for PBXProject "JDTMyButtonModule" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F422DD00000040 /* Debug */,
				F422DD00000050 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		F422DD00000140 /* Build configuration list for PBXNativeTarget "JDTMyButtonModule" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F422DD00000160 /* Debug */,
				F422DD00000150 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		F422DD00000200 /* Build configuration list for PBXNativeTarget "JDTMyButtonModule-JDTMyButtonModule" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F422DD00000220 /* Debug */,
				F422DD00000210 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = F422DD00000000 /* Project object */;
}
