//
//  ViewController.m
//  CMSDemo
//
//  Created by lvchenzhu.1 on 2025/5/6.
//

#import "ViewController.h"
@import JDTMyButtonModule;

@interface ViewController ()

@property (nonatomic, strong) UILabel *label;

@property (nonatomic, strong) MyLabel *myLabel;

@end

@implementation ViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.label = [[UILabel alloc] initWithFrame:CGRectMake(100, 200, 300, 200)];
    self.label.text = NSLocalizedString(@"label", @"");
    [self.view addSubview:self.label];
    
    self.myLabel = [[MyLabel alloc] initWithFrame:CGRectMake(100, 500, 300, 200)];
    [self.view addSubview:self.myLabel];
}


@end
